export interface ExcelRow {
  'Lp.': number;
  'Naz<PERSON>sko i imię': string;
  'Nr Karty': string;
  'Data': string;
  'Punkt KD': string;
  'Kierunek': string;
  'Typ': string;
  'Opis': string;
}

export interface ProcessedEntry {
  id: string;
  timestamp: Date;
  action: 'Wejście' | 'Wyjście';
}

export interface BreakPeriod {
  start: Date;
  end: Date;
  duration: number; // в минутах
}

export interface EmployeeStatistics {
  id: string;
  totalTimeMinutes: number;
  longestBreakMinutes: number;
  shortestBreakMinutes: number;
  breaks: BreakPeriod[];
}
