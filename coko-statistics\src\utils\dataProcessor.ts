// Типы данных
export interface ExcelRow {
  'Lp.': number;
  'Nazwisko i imię': string;
  'Nr Karty': string;
  'Data': string;
  'Punkt KD': string;
  'Kierunek': string;
  'Typ': string;
  'Opis': string;
}

export interface ProcessedEntry {
  id: string;
  timestamp: Date;
  action: 'Wejście' | 'Wyjście';
}

export interface BreakPeriod {
  start: Date;
  end: Date;
  duration: number; // в минутах
}

export interface EmployeeStatistics {
  id: string;
  totalTimeMinutes: number;
  longestBreakMinutes: number;
  shortestBreakMinutes: number;
  breaks: BreakPeriod[];
}

export function parseExcelData(data: any[]): ProcessedEntry[] {
  const entries: ProcessedEntry[] = [];
  
  // Пропускаем заголовок (первая строка)
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    
    // Проверяем, что строка содержит необходимые данные
    if (!row || !row[2] || !row[3] || !row[6]) continue;
    
    const id = String(row[2]); // Nr Karty
    const dateStr = String(row[3]); // Data
    const direction = String(row[6]); // Kierunek
    
    // Парсим дату
    const timestamp = parseDate(dateStr);
    if (!timestamp) continue;
    
    // Определяем действие
    const action = direction === 'Wejście' ? 'Wejście' : 'Wyjście';
    
    entries.push({
      id,
      timestamp,
      action
    });
  }
  
  // Сортируем по времени
  return entries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

function parseDate(dateStr: string): Date | null {
  try {
    // Формат: "01.08.2025 03:25:11"
    const [datePart, timePart] = dateStr.split(' ');
    const [day, month, year] = datePart.split('.');
    const [hours, minutes, seconds] = timePart.split(':');
    
    return new Date(
      parseInt(year),
      parseInt(month) - 1, // месяцы в JS начинаются с 0
      parseInt(day),
      parseInt(hours),
      parseInt(minutes),
      parseInt(seconds)
    );
  } catch (error) {
    console.error('Error parsing date:', dateStr, error);
    return null;
  }
}

export function calculateStatistics(entries: ProcessedEntry[]): EmployeeStatistics[] {
  const employeeMap = new Map<string, ProcessedEntry[]>();
  
  // Группируем записи по ID сотрудника
  entries.forEach(entry => {
    if (!employeeMap.has(entry.id)) {
      employeeMap.set(entry.id, []);
    }
    employeeMap.get(entry.id)!.push(entry);
  });
  
  const statistics: EmployeeStatistics[] = [];
  
  employeeMap.forEach((employeeEntries, id) => {
    // Сортируем записи сотрудника по времени
    employeeEntries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    let totalTimeMinutes = 0;
    let currentEntryTime: Date | null = null;
    const breaks: BreakPeriod[] = [];
    
    employeeEntries.forEach(entry => {
      if (entry.action === 'Wejście') {
        currentEntryTime = entry.timestamp;
      } else if (entry.action === 'Wyjście' && currentEntryTime) {
        // Добавляем время пребывания
        const sessionMinutes = (entry.timestamp.getTime() - currentEntryTime.getTime()) / (1000 * 60);
        totalTimeMinutes += sessionMinutes;
        
        // Ищем следующий вход для расчета перекура
        const nextEntryIndex = employeeEntries.findIndex(e => 
          e.timestamp > entry.timestamp && e.action === 'Wejście'
        );
        
        if (nextEntryIndex !== -1) {
          const nextEntry = employeeEntries[nextEntryIndex];
          const breakDuration = (nextEntry.timestamp.getTime() - entry.timestamp.getTime()) / (1000 * 60);
          
          breaks.push({
            start: entry.timestamp,
            end: nextEntry.timestamp,
            duration: breakDuration
          });
        }
        
        currentEntryTime = null;
      }
    });
    
    // Вычисляем самый длинный и короткий перекур
    let longestBreakMinutes = 0;
    let shortestBreakMinutes = Infinity;
    
    if (breaks.length > 0) {
      breaks.forEach(breakPeriod => {
        longestBreakMinutes = Math.max(longestBreakMinutes, breakPeriod.duration);
        shortestBreakMinutes = Math.min(shortestBreakMinutes, breakPeriod.duration);
      });
    } else {
      shortestBreakMinutes = 0;
    }
    
    statistics.push({
      id,
      totalTimeMinutes,
      longestBreakMinutes,
      shortestBreakMinutes: shortestBreakMinutes === Infinity ? 0 : shortestBreakMinutes,
      breaks
    });
  });
  
  return statistics.sort((a, b) => a.id.localeCompare(b.id));
}

export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  
  if (hours > 0) {
    return `${hours}ч ${mins}мин`;
  }
  return `${mins}мин`;
}
