"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2022_regexp = void 0;
const base_config_1 = require("./base-config");
exports.es2022_regexp = {
    libs: [],
    variables: [
        ['RegExpMatchArray', base_config_1.TYPE],
        ['RegExpExecArray', base_config_1.TYPE],
        ['RegExpIndicesArray', base_config_1.TYPE],
        ['RegExp', base_config_1.TYPE],
    ],
};
