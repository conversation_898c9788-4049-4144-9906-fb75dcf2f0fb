.excel-analyzer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.upload-section {
  text-align: center;
  margin-bottom: 40px;
}

.upload-section h1 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2.5rem;
}

.file-input-wrapper {
  margin-bottom: 20px;
}

#file-input {
  display: none;
}

.file-input-label {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.file-input-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.file-input-label:active {
  transform: translateY(0);
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #fcc;
  margin-top: 10px;
}

.test-file-section {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.test-file-section p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.test-file-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-file-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.results-section {
  margin-top: 40px;
}

.results-section h2 {
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
  text-align: center;
}

.statistics-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.employee-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  transition: all 0.3s ease;
}

.employee-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.employee-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.employee-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.employee-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.stat-label {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
}

/* Адаптивность */
@media (max-width: 768px) {
  .excel-analyzer {
    padding: 15px;
  }
  
  .upload-section h1 {
    font-size: 2rem;
  }
  
  .statistics-list {
    grid-template-columns: 1fr;
  }
  
  .employee-card {
    padding: 16px;
  }
  
  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .stat-value {
    align-self: flex-end;
  }
}
