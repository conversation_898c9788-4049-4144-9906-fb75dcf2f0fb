{"version": 3, "names": ["warnings", "Set", "deprecationWarning", "old<PERSON>ame", "newName", "prefix", "cache<PERSON>ey", "has", "add", "internal", "trace", "captureShortStackTrace", "console", "warn", "skip", "length", "stackTraceLimit", "prepareStackTrace", "Error", "stackTrace", "err", "stack", "shortStackTrace", "slice", "test", "getFileName", "map", "frame", "join"], "sources": ["../../src/utils/deprecationWarning.ts"], "sourcesContent": ["const warnings = new Set();\n\nexport default function deprecationWarning(\n  oldName: string,\n  newName: string,\n  prefix: string = \"\",\n  cacheKey: string = oldName,\n) {\n  if (warnings.has(cacheKey)) return;\n  warnings.add(cacheKey);\n\n  const { internal, trace } = captureShortStackTrace(1, 2);\n  if (internal) {\n    // If usage comes from an internal package, there is no point in warning because\n    // 1. The new version of the package will already use the new API\n    // 2. When the deprecation will become an error (in a future major version), users\n    //    will have to update every package anyway.\n    return;\n  }\n  console.warn(\n    `${prefix}\\`${oldName}\\` has been deprecated, please migrate to \\`${newName}\\`\\n${trace}`,\n  );\n}\n\nfunction captureShortStackTrace(skip: number, length: number) {\n  const { stackTraceLimit, prepareStackTrace } = Error;\n  let stackTrace: NodeJS.CallSite[];\n  // We add 1 to also take into account this function.\n  Error.stackTraceLimit = 1 + skip + length;\n  Error.prepareStackTrace = function (err, stack) {\n    stackTrace = stack;\n  };\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  new Error().stack;\n  Error.stackTraceLimit = stackTraceLimit;\n  Error.prepareStackTrace = prepareStackTrace;\n\n  if (!stackTrace) return { internal: false, trace: \"\" };\n\n  const shortStackTrace = stackTrace.slice(1 + skip, 1 + skip + length);\n  return {\n    internal: /[\\\\/]@babel[\\\\/]/.test(shortStackTrace[1].getFileName()),\n    trace: shortStackTrace.map(frame => `    at ${frame}`).join(\"\\n\"),\n  };\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEX,SAASC,kBAAkBA,CACxCC,OAAe,EACfC,OAAe,EACfC,MAAc,GAAG,EAAE,EACnBC,QAAgB,GAAGH,OAAO,EAC1B;EACA,IAAIH,QAAQ,CAACO,GAAG,CAACD,QAAQ,CAAC,EAAE;EAC5BN,QAAQ,CAACQ,GAAG,CAACF,QAAQ,CAAC;EAEtB,MAAM;IAAEG,QAAQ;IAAEC;EAAM,CAAC,GAAGC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,IAAIF,QAAQ,EAAE;IAKZ;EACF;EACAG,OAAO,CAACC,IAAI,CACV,GAAGR,MAAM,KAAKF,OAAO,+CAA+CC,OAAO,OAAOM,KAAK,EACzF,CAAC;AACH;AAEA,SAASC,sBAAsBA,CAACG,IAAY,EAAEC,MAAc,EAAE;EAC5D,MAAM;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGC,KAAK;EACpD,IAAIC,UAA6B;EAEjCD,KAAK,CAACF,eAAe,GAAG,CAAC,GAAGF,IAAI,GAAGC,MAAM;EACzCG,KAAK,CAACD,iBAAiB,GAAG,UAAUG,GAAG,EAAEC,KAAK,EAAE;IAC9CF,UAAU,GAAGE,KAAK;EACpB,CAAC;EAED,IAAIH,KAAK,CAAC,CAAC,CAACG,KAAK;EACjBH,KAAK,CAACF,eAAe,GAAGA,eAAe;EACvCE,KAAK,CAACD,iBAAiB,GAAGA,iBAAiB;EAE3C,IAAI,CAACE,UAAU,EAAE,OAAO;IAAEV,QAAQ,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAG,CAAC;EAEtD,MAAMY,eAAe,GAAGH,UAAU,CAACI,KAAK,CAAC,CAAC,GAAGT,IAAI,EAAE,CAAC,GAAGA,IAAI,GAAGC,MAAM,CAAC;EACrE,OAAO;IACLN,QAAQ,EAAE,kBAAkB,CAACe,IAAI,CAACF,eAAe,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;IACnEf,KAAK,EAAEY,eAAe,CAACI,GAAG,CAACC,KAAK,IAAI,UAAUA,KAAK,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI;EAClE,CAAC;AACH", "ignoreList": []}