"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2019_array = void 0;
const base_config_1 = require("./base-config");
exports.es2019_array = {
    libs: [],
    variables: [
        ['FlatArray', base_config_1.TYPE],
        ['ReadonlyArray', base_config_1.TYPE],
        ['Array', base_config_1.TYPE],
    ],
};
