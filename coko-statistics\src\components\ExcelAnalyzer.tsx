import React, { useState } from 'react';
import * as XLSX from 'xlsx';
import { EmployeeStatistics, parseExcelData, calculateStatistics, formatDuration } from '../utils/dataProcessor';
import { downloadTestFile } from '../utils/createTestData';
import './ExcelAnalyzer.css';

const ExcelAnalyzer: React.FC = () => {
  const [statistics, setStatistics] = useState<EmployeeStatistics[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data, { type: 'array' });
      
      // Читаем первый лист
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      
      // Конвертируем в JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // Обрабатываем данные
      const entries = parseExcelData(jsonData);
      const stats = calculateStatistics(entries);
      
      setStatistics(stats);
    } catch (err) {
      setError('Ошибка при обработке файла: ' + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="excel-analyzer">
      <div className="upload-section">
        <h1>Анализ статистики посещений</h1>
        <div className="file-input-wrapper">
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={handleFileUpload}
            disabled={loading}
            id="file-input"
          />
          <label htmlFor="file-input" className="file-input-label">
            {loading ? 'Обработка...' : 'Выберите Excel файл (.xlsx)'}
          </label>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <div className="test-file-section">
          <p>Нет тестового файла? Создайте его:</p>
          <button
            className="test-file-button"
            onClick={downloadTestFile}
          >
            Скачать тестовый Excel файл
          </button>
        </div>
      </div>

      {statistics.length > 0 && (
        <div className="results-section">
          <h2>Результаты анализа ({statistics.length} сотрудников)</h2>
          <div className="statistics-list">
            {statistics.map((stat) => (
              <div key={stat.id} className="employee-card">
                <div className="employee-header">
                  <h3>ID: {stat.id}</h3>
                </div>
                <div className="employee-stats">
                  <div className="stat-item">
                    <span className="stat-label">Общее время:</span>
                    <span className="stat-value">{formatDuration(stat.totalTimeMinutes)}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Длинный перекур:</span>
                    <span className="stat-value">
                      {stat.longestBreakMinutes > 0 
                        ? formatDuration(stat.longestBreakMinutes)
                        : 'Нет данных'
                      }
                    </span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Короткий перекур:</span>
                    <span className="stat-value">
                      {stat.shortestBreakMinutes > 0 
                        ? formatDuration(stat.shortestBreakMinutes)
                        : 'Нет данных'
                      }
                    </span>
                  </div>
                  {stat.breaks.length > 0 && (
                    <div className="stat-item">
                      <span className="stat-label">Количество перекуров:</span>
                      <span className="stat-value">{stat.breaks.length}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExcelAnalyzer;
