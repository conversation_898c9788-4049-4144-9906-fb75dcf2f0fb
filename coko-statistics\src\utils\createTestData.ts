import * as XLSX from 'xlsx';

export function createTestExcelFile(): void {
  // Создаем тестовые данные в формате, соответствующем примеру
  const testData = [
    // Заголовок
    ['Lp.', 'Nazwisko i imię', 'Nr Karty', 'Data', '', 'Punkt KD', 'Kierunek', '', 'Typ', 'Opis'],
    
    // Тестовые данные для сотрудника 30163
    [1, 'Karta 30163', '30163', '01.08.2025 08:00:00', '', 'LANG RCP Szatnia', 'Wej<PERSON><PERSON>', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [2, 'Karta 30163', '30163', '01.08.2025 12:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [3, 'Karta 30163', '30163', '01.08.2025 12:30:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [4, 'Karta 30163', '30163', '01.08.2025 17:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    
    // Тестовые данные для сотрудника 80055
    [5, 'Karta 80055', '80055', '01.08.2025 09:00:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [6, 'Karta 80055', '80055', '01.08.2025 11:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [7, 'Karta 80055', '80055', '01.08.2025 11:15:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [8, 'Karta 80055', '80055', '01.08.2025 14:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [9, 'Karta 80055', '80055', '01.08.2025 15:00:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [10, 'Karta 80055', '80055', '01.08.2025 18:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    
    // Тестовые данные для сотрудника 80088
    [11, 'Karta 80088', '80088', '01.08.2025 07:30:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [12, 'Karta 80088', '80088', '01.08.2025 16:30:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
  ];

  // Создаем рабочую книгу
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(testData);
  
  // Добавляем лист в книгу
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Данные');
  
  // Сохраняем файл
  XLSX.writeFile(workbook, 'test-data.xlsx');
  
  console.log('Тестовый файл test-data.xlsx создан!');
}

// Функция для создания файла через кнопку в интерфейсе
export function downloadTestFile(): void {
  const testData = [
    ['Lp.', 'Nazwisko i imię', 'Nr Karty', 'Data', '', 'Punkt KD', 'Kierunek', '', 'Typ', 'Opis'],
    [1, 'Karta 30163', '30163', '01.08.2025 08:00:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [2, 'Karta 30163', '30163', '01.08.2025 12:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [3, 'Karta 30163', '30163', '01.08.2025 12:30:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [4, 'Karta 30163', '30163', '01.08.2025 17:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [5, 'Karta 80055', '80055', '01.08.2025 09:00:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [6, 'Karta 80055', '80055', '01.08.2025 11:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [7, 'Karta 80055', '80055', '01.08.2025 11:15:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [8, 'Karta 80055', '80055', '01.08.2025 14:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [9, 'Karta 80055', '80055', '01.08.2025 15:00:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [10, 'Karta 80055', '80055', '01.08.2025 18:00:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [11, 'Karta 80088', '80088', '01.08.2025 07:30:00', '', 'LANG RCP Szatnia', 'Wejście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
    [12, 'Karta 80088', '80088', '01.08.2025 16:30:00', '', 'LANG RCP Szatnia', 'Wyjście', '', 'zwykłe', 'pracownik nie jest poszukiwany'],
  ];

  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(testData);
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Данные');
  
  // Скачиваем файл
  XLSX.writeFile(workbook, 'test-data.xlsx');
}
